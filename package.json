{"name": "forever-fest-2026", "version": "1.0.0", "private": true, "sideEffects": false, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748", "engines": {"node": ">=22", "pnpm": ">=10", "npm": "please-use-pnpm", "yarn": "please-use-pnpm"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint --fix"}, "dependencies": {"@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-tooltip": "1.1.6", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "lucide-react": "^0.454.0", "next": "^15.4.5", "next-themes": "^0.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.32.0", "@next/bundle-analyzer": "^15.4.5", "@next/eslint-plugin-next": "^15.4.5", "@stylistic/eslint-plugin": "^5.2.2", "@types/node": "^22.17.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-config-next": "15.3.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.9.2", "typescript-eslint": "^8.38.0"}, "browserslist": ["Chrome >= 109", "Firefox >= 109", "Safari >= 16.4", "Edge >= 109"]}